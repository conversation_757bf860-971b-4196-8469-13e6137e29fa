{"name": "lms-mobile-app", "version": "1.0.0", "description": "LMS Mobile Application - React Native", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace LMSApp.xcworkspace -scheme LMSApp -configuration Release archive", "clean": "react-native clean-project-auto", "pod-install": "cd ios && pod install"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.19.3", "@react-native-camera-roll/camera-roll": "^5.7.4", "@react-native-community/netinfo": "^9.4.1", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-firebase/app": "^18.6.1", "@react-native-firebase/messaging": "^18.6.1", "@react-native-firebase/analytics": "^18.6.1", "@react-native-firebase/crashlytics": "^18.6.1", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@reduxjs/toolkit": "^1.9.7", "react": "18.2.0", "react-native": "0.72.6", "react-native-background-job": "^1.2.4", "react-native-biometrics": "^3.0.1", "react-native-bootsplash": "^4.7.5", "react-native-camera": "^4.2.1", "react-native-document-picker": "^9.1.1", "react-native-fast-image": "^8.6.3", "react-native-file-viewer": "^2.1.5", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.13.4", "react-native-image-picker": "^7.0.3", "react-native-keychain": "^8.1.3", "react-native-linear-gradient": "^2.8.3", "react-native-mmkv": "^2.10.2", "react-native-orientation-locker": "^1.5.0", "react-native-paper": "^5.11.1", "react-native-permissions": "^3.10.1", "react-native-push-notification": "^8.1.1", "react-native-reanimated": "^3.5.4", "react-native-safe-area-context": "^4.7.4", "react-native-screens": "^3.27.0", "react-native-share": "^9.4.1", "react-native-sound": "^0.11.2", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^13.14.0", "react-native-vector-icons": "^10.0.2", "react-native-video": "^5.2.1", "react-native-webview": "^13.6.3", "react-redux": "^8.1.3", "redux-persist": "^6.0.0", "socket.io-client": "^4.7.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}, "keywords": ["react-native", "mobile", "lms", "education", "learning", "offline", "ios", "android"], "author": "LMS Development Team", "license": "MIT"}