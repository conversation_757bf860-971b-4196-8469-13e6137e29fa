# Production Environment Variables
# Copy this file to .env.production and fill in your values

# GitHub Repository (for Docker images)
GITHUB_REPOSITORY=your-username/lms

# Database Configuration
POSTGRES_DB=lms_production
POSTGRES_USER=lms_user
POSTGRES_PASSWORD=your-secure-database-password-here

# Redis Configuration
REDIS_PASSWORD=your-secure-redis-password-here

# JWT Configuration (generate secure random strings)
JWT_SECRET=your-super-secure-jwt-secret-min-32-chars-here
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-min-32-chars-here

# Application URLs
FRONTEND_URL=https://yourdomain.com
BACKEND_URL=https://api.yourdomain.com

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=Your LMS

# File Upload (Cloudinary)
CLOUDINARY_CLOUD_NAME=your-cloudinary-name
CLOUDINARY_API_KEY=your-cloudinary-key
CLOUDINARY_API_SECRET=your-cloudinary-secret

# Monitoring (optional)
GRAFANA_PASSWORD=your-grafana-admin-password

# Staging URLs (for staging environment)
STAGING_FRONTEND_URL=https://staging.yourdomain.com
STAGING_BACKEND_URL=https://staging-api.yourdomain.com
