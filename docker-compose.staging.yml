version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: lms-postgres-staging
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-lms_staging}
      POSTGRES_USER: ${POSTGRES_USER:-lms_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_staging_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    networks:
      - lms-staging-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-lms_user} -d ${POSTGRES_DB:-lms_staging}"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    container_name: lms-redis-staging
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_staging_data:/data
    ports:
      - "6380:6379"
    networks:
      - lms-staging-network
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend:
    image: ghcr.io/${GITHUB_REPOSITORY:-your-username/lms}-backend:develop
    container_name: lms-backend-staging
    restart: unless-stopped
    environment:
      NODE_ENV: staging
      PORT: 5000
      DATABASE_URL: postgresql://${POSTGRES_USER:-lms_user}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-lms_staging}
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
      JWT_SECRET: ${JWT_SECRET}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET}
      FRONTEND_URL: ${STAGING_FRONTEND_URL}
      SMTP_HOST: ${SMTP_HOST}
      SMTP_PORT: ${SMTP_PORT}
      SMTP_USER: ${SMTP_USER}
      SMTP_PASS: ${SMTP_PASS}
      CLOUDINARY_CLOUD_NAME: ${CLOUDINARY_CLOUD_NAME}
      CLOUDINARY_API_KEY: ${CLOUDINARY_API_KEY}
      CLOUDINARY_API_SECRET: ${CLOUDINARY_API_SECRET}
      LOG_LEVEL: debug
    ports:
      - "5001:5000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - lms-staging-network
    volumes:
      - ./logs-staging:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  frontend:
    image: ghcr.io/${GITHUB_REPOSITORY:-your-username/lms}-frontend:develop
    container_name: lms-frontend-staging
    restart: unless-stopped
    ports:
      - "8080:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - lms-staging-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_staging_data:
    driver: local
  redis_staging_data:
    driver: local

networks:
  lms-staging-network:
    driver: bridge
