{"name": "modern-lms-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report"}, "dependencies": {"axios": "^1.6.0", "clsx": "^2.1.1", "date-fns": "^2.30.0", "framer-motion": "^10.16.4", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.47.0", "react-router-dom": "^6.26.1", "tailwind-merge": "^3.3.1", "zustand": "^4.4.6"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^6.3.5", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "@vitest/ui": "^2.0.0", "@vitest/coverage-v8": "^2.0.0", "jsdom": "^23.0.1", "msw": "^2.0.11", "vitest": "^2.0.0", "vitest-mock-extended": "^1.3.1", "@playwright/test": "^1.40.1"}}