{"name": "lms-backend", "version": "1.0.0", "description": "LMS Backend API Server", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon src/server.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:seed": "npx prisma db seed", "db:studio": "npx prisma studio", "db:reset": "npx prisma migrate reset"}, "dependencies": {"@prisma/client": "^5.6.0", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.9.0", "@types/nodemailer": "^6.4.14", "@types/socket.io": "^3.0.2", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "redis": "^4.6.10", "socket.io": "^4.7.4", "stripe": "^14.7.0", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "prisma": "^5.6.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "keywords": ["lms", "education", "learning", "api", "backend", "express", "typescript", "prisma"], "author": "LMS Development Team", "license": "MIT"}