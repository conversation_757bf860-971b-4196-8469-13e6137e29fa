name: LMS CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: lms_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: |
        npm ci
        cd backend && npm ci

    - name: Setup test environment
      run: |
        cd backend
        cp .env.example .env.test
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/lms_test" >> .env.test
        echo "REDIS_URL=redis://localhost:6379" >> .env.test
        echo "JWT_SECRET=test-jwt-secret-for-testing-only" >> .env.test
        echo "JWT_REFRESH_SECRET=test-refresh-secret-for-testing-only" >> .env.test

    - name: Generate Prisma client
      run: |
        cd backend
        npx prisma generate

    - name: Run database migrations
      run: |
        cd backend
        npx prisma migrate deploy
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/lms_test

    - name: Run backend tests
      run: |
        cd backend
        npm test
      env:
        NODE_ENV: test
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/lms_test

    - name: Run frontend tests
      run: npm test -- --coverage --watchAll=false

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        token: ${{ secrets.CODECOV_TOKEN }}
        files: ./coverage/lcov.info,./backend/coverage/lcov.info

  build:
    name: Build Applications
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: |
        npm ci
        cd backend && npm ci

    - name: Build frontend
      run: npm run build
      env:
        REACT_APP_API_URL: ${{ secrets.REACT_APP_API_URL }}
        REACT_APP_SOCKET_URL: ${{ secrets.REACT_APP_SOCKET_URL }}

    - name: Build backend
      run: |
        cd backend
        npm run build

    - name: Upload frontend build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: frontend-build
        path: build/

    - name: Upload backend build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: backend-build
        path: backend/dist/

  docker:
    name: Build and Push Docker Images
    runs-on: ubuntu-latest
    needs: [test, build]
    if: github.ref == 'refs/heads/main'
    
    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata for frontend
      id: meta-frontend
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-frontend
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Extract metadata for backend
      id: meta-backend
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push frontend image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.frontend
        push: true
        tags: ${{ steps.meta-frontend.outputs.tags }}
        labels: ${{ steps.meta-frontend.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Build and push backend image
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        file: ./backend/Dockerfile
        push: true
        tags: ${{ steps.meta-backend.outputs.tags }}
        labels: ${{ steps.meta-backend.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [docker]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Deploy to staging server
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.STAGING_HOST }}
        username: ${{ secrets.STAGING_USER }}
        key: ${{ secrets.STAGING_SSH_KEY }}
        script: |
          cd /home/<USER>/lms-app
          docker-compose -f docker-compose.staging.yml pull
          docker-compose -f docker-compose.staging.yml up -d
          docker system prune -f

    - name: Run health check
      run: |
        sleep 30
        curl -f ${{ secrets.STAGING_URL }}/health || exit 1

    - name: Notify Slack on success
      if: success()
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: "✅ Staging deployment successful!"
        webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [docker]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to production server
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.PRODUCTION_HOST }}
        username: ${{ secrets.PRODUCTION_USER }}
        key: ${{ secrets.PRODUCTION_SSH_KEY }}
        script: |
          cd /home/<USER>/lms-app
          
          # Backup current deployment
          docker-compose -f docker-compose.prod.yml exec -T postgres pg_dump -U $POSTGRES_USER $POSTGRES_DB > backup-$(date +%Y%m%d-%H%M%S).sql
          
          # Pull new images
          docker-compose -f docker-compose.prod.yml pull
          
          # Zero-downtime deployment
          docker-compose -f docker-compose.prod.yml up -d --no-deps backend
          sleep 10
          docker-compose -f docker-compose.prod.yml up -d --no-deps frontend
          
          # Clean up old images
          docker system prune -f

    - name: Run production health check
      run: |
        sleep 60
        curl -f ${{ secrets.PRODUCTION_URL }}/health || exit 1

    - name: Run database migrations
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.PRODUCTION_HOST }}
        username: ${{ secrets.PRODUCTION_USER }}
        key: ${{ secrets.PRODUCTION_SSH_KEY }}
        script: |
          cd /home/<USER>/lms-app
          docker-compose -f docker-compose.prod.yml exec -T backend npx prisma migrate deploy

    - name: Notify Slack on success
      if: success()
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: "🚀 Production deployment successful!"
        webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Rollback on failure
      if: failure()
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.PRODUCTION_HOST }}
        username: ${{ secrets.PRODUCTION_USER }}
        key: ${{ secrets.PRODUCTION_SSH_KEY }}
        script: |
          cd /home/<USER>/lms-app
          # Rollback to previous version
          docker-compose -f docker-compose.prod.yml down
          docker-compose -f docker-compose.prod.yml up -d

  notify-failure:
    name: Notify on Failure
    runs-on: ubuntu-latest
    needs: [test, build, docker, deploy-production]
    if: failure()
    
    steps:
    - name: Notify Slack on failure
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        text: "❌ Deployment failed! Check the logs for details."
        webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}
